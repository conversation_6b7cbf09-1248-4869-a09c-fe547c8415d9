'use client';
import React, { useState } from 'react';
import {
	ChevronLeft,
	ChevronRight,
	Heart,
	MessageCircle,
	User,
} from 'lucide-react';
import { CustomImage } from '../common';

const ProfileCarousel = ({ profiles }) => {
	const [currentProfile, setCurrentProfile] = useState(0);

	console.log('Carrosel', profiles);

	const nextProfile = () => {
		setCurrentProfile((prev) => (prev + 1) % profiles.length);
	};

	const prevProfile = () => {
		setCurrentProfile((prev) => (prev - 1 + profiles.length) % profiles.length);
	};

	const currentProfileData = profiles[currentProfile];

	return (
		<div className='flex items-center justify-center'>
			<div className='relative bg-white rounded-3xl shadow-2xl overflow-hidden max-w-sm w-full'>
				{/* Profile Image Section */}
				<div className='relative h-96 bg-gradient-to-b from-amber-100 to-amber-200'>
					<CustomImage
						src={currentProfileData.avatar}
						alt={currentProfileData.name}
						width={100}
						height={100}
						className='w-full h-full object-cover'
					/>

					{/* Navigation Arrows */}
					<button
						onClick={prevProfile}
						className='absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110'>
						<ChevronLeft className='w-6 h-6 text-gray-700 cursor-pointer' />
					</button>

					<button
						onClick={nextProfile}
						className='absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110'>
						<ChevronRight className='w-6 h-6 text-gray-700 cursor-pointer' />
					</button>

					{/* Profile Indicators */}
					<div className='absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2'>
						{profiles.map((_, index) => (
							<button
								key={index}
								onClick={() => setCurrentProfile(index)}
								className={`w-2 h-2 rounded-full transition-all duration-200 ${
									index === currentProfile
										? 'bg-white scale-125'
										: 'bg-white/50'
								}`}
							/>
						))}
					</div>
				</div>

				{/* Profile Info Section */}
				<div className='p-6 bg-white'>
					<div className='text-center mb-6'>
						<h2 className='text-2xl font-bold text-gray-800 mb-1'>
							{currentProfileData.name}
						</h2>
					</div>

					{/* Action Buttons */}
					<div className='flex space-x-3'>
						<button className='flex items-center justify-center w-16 h-16 bg-gray-100 hover:bg-gray-200 rounded-full transition-all duration-200 hover:scale-105'>
							<Heart className='w-6 h-6 text-gray-600' />
						</button>

						<button className='flex-1 bg-slate-800 hover:bg-slate-700 text-white py-4 px-6 rounded-full font-semibold transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-2'>
							<MessageCircle className='w-5 h-5' />
							<span>Message</span>
						</button>
					</div>

					<button className='w-full mt-3 bg-teal-500 hover:bg-teal-600 text-white py-4 px-6 rounded-full font-semibold transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-2'>
						<User className='w-5 h-5' />
						<span>Profile</span>
					</button>
				</div>

				{/* Profile Counter */}
				<div className='absolute top-4 right-4 bg-black/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm'>
					{currentProfile + 1} / {profiles.length}
				</div>
			</div>
		</div>
	);
};

export default ProfileCarousel;
