'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useDarkMode } from '@/contexts/DarkModeContext';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import { useUserProfile } from '@/src/lib/hooks';
import { userAPI } from '@/src/lib/api';
import { FaSave, FaTimes, FaSpinner } from 'react-icons/fa';

const EditProfile = () => {
	const router = useRouter();
	const { isDarkMode } = useDarkMode();
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [error, setError] = useState('');
	const [success, setSuccess] = useState('');

	const {
		data: userProfile,
		isLoading: isUserLoading,
		error: userError,
		refetch: refetchProfile,
	} = useUserProfile();

	const [formData, setFormData] = useState({
		name: '',
		dateOfBirth: '',
		mobile: '',
		location: {
			country: '',
			state: '',
			city: '',
			address: '',
		},
		aboutMe: '',
	});

	// Populate form with current user data
	useEffect(() => {
		if (userProfile?.user) {
			const user = userProfile.user;
			setFormData({
				name: user.name || '',
				dateOfBirth: user.dateOfBirth ? user.dateOfBirth.split('T')[0] : '',
				mobile: user.mobile || '',
				location: {
					country: user.location?.country || '',
					state: user.location?.state || '',
					city: user.location?.city || '',
					address: user.location?.address || '',
				},
				aboutMe: user.aboutMe || '',
			});
		}
	}, [userProfile]);

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	const handleInputChange = (e) => {
		const { name, value } = e.target;
		
		if (name.startsWith('location.')) {
			const locationField = name.split('.')[1];
			setFormData(prev => ({
				...prev,
				location: {
					...prev.location,
					[locationField]: value,
				},
			}));
		} else {
			setFormData(prev => ({
				...prev,
				[name]: value,
			}));
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setIsSubmitting(true);
		setError('');
		setSuccess('');

		try {
			// Prepare data for submission
			const updateData = {
				name: formData.name.trim(),
				dateOfBirth: formData.dateOfBirth || null,
				mobile: formData.mobile.trim() || null,
				location: {
					country: formData.location.country.trim() || null,
					state: formData.location.state.trim() || null,
					city: formData.location.city.trim() || null,
					address: formData.location.address.trim() || null,
				},
				aboutMe: formData.aboutMe.trim() || null,
			};

			// Remove empty location object if all fields are null
			if (Object.values(updateData.location).every(val => val === null)) {
				updateData.location = null;
			}

			await userAPI.updateProfileDetails(updateData);
			
			setSuccess('Profile updated successfully!');
			
			// Refetch profile data
			await refetchProfile();
			
			// Redirect back to profile after a short delay
			setTimeout(() => {
				router.push('/profile');
			}, 2000);
			
		} catch (err) {
			console.error('Profile update error:', err);
			setError(err.response?.data?.message || 'Failed to update profile. Please try again.');
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleCancel = () => {
		router.push('/profile');
	};

	if (isUserLoading) {
		return (
			<div className='min-h-screen bg-white dark:bg-[#0C142A] transition-colors duration-300'>
				<FixedHeader title='Edit Profile' toggleSidebar={toggleSidebar} />
				<div className='container mx-auto px-4 py-8 pt-20'>
					<div className='max-w-2xl mx-auto'>
						<div className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6'>
							<div className='animate-pulse space-y-6'>
								{[...Array(6)].map((_, i) => (
									<div key={i} className='space-y-2'>
										<div className='h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded'></div>
										<div className='h-10 w-full bg-gray-200 dark:bg-gray-700 rounded'></div>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-white dark:bg-[#0C142A] transition-colors duration-300'>
			<FixedHeader title='Edit Profile' toggleSidebar={toggleSidebar} />
			<div className='container mx-auto px-4 py-8 pt-20'>
				<div className='max-w-2xl mx-auto'>
					<div className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6'>
							Edit Your Profile
						</h2>

						{error && (
							<div className='mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg'>
								<p className='text-red-600 dark:text-red-400 text-sm'>{error}</p>
							</div>
						)}

						{success && (
							<div className='mb-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg'>
								<p className='text-green-600 dark:text-green-400 text-sm'>{success}</p>
							</div>
						)}

						<form onSubmit={handleSubmit} className='space-y-6'>
							{/* Name */}
							<div>
								<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
									Full Name *
								</label>
								<input
									type='text'
									name='name'
									value={formData.name}
									onChange={handleInputChange}
									required
									maxLength={50}
									className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
									placeholder='Enter your full name'
								/>
							</div>

							{/* Date of Birth */}
							<div>
								<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
									Date of Birth
								</label>
								<input
									type='date'
									name='dateOfBirth'
									value={formData.dateOfBirth}
									onChange={handleInputChange}
									max={new Date().toISOString().split('T')[0]}
									className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
								/>
							</div>

							{/* Mobile */}
							<div>
								<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
									Mobile Number
								</label>
								<input
									type='tel'
									name='mobile'
									value={formData.mobile}
									onChange={handleInputChange}
									pattern='[\+]?[0-9\s\-\(\)]{10,15}'
									className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
									placeholder='Enter your mobile number'
								/>
								<p className='text-xs text-gray-500 dark:text-gray-400 mt-1'>
									Format: +1234567890 or (*************
								</p>
							</div>

							{/* Location */}
							<div className='space-y-4'>
								<h3 className='text-lg font-medium text-gray-800 dark:text-gray-200'>
									Location
								</h3>
								
								<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
									<div>
										<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
											Country
										</label>
										<input
											type='text'
											name='location.country'
											value={formData.location.country}
											onChange={handleInputChange}
											maxLength={50}
											className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
											placeholder='Country'
										/>
									</div>
									
									<div>
										<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
											State/Province
										</label>
										<input
											type='text'
											name='location.state'
											value={formData.location.state}
											onChange={handleInputChange}
											maxLength={50}
											className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
											placeholder='State or Province'
										/>
									</div>
								</div>

								<div>
									<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
										City
									</label>
									<input
										type='text'
										name='location.city'
										value={formData.location.city}
										onChange={handleInputChange}
										maxLength={50}
										className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
										placeholder='City'
									/>
								</div>

								<div>
									<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
										Address
									</label>
									<input
										type='text'
										name='location.address'
										value={formData.location.address}
										onChange={handleInputChange}
										maxLength={200}
										className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
										placeholder='Detailed address'
									/>
								</div>
							</div>

							{/* About Me */}
							<div>
								<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
									About Me
								</label>
								<textarea
									name='aboutMe'
									value={formData.aboutMe}
									onChange={handleInputChange}
									maxLength={500}
									rows={4}
									className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none'
									placeholder='Tell us about yourself (max 500 characters)'
								/>
								<p className='text-xs text-gray-500 dark:text-gray-400 mt-1'>
									{formData.aboutMe.length}/500 characters
								</p>
							</div>

							{/* Action Buttons */}
							<div className='flex gap-4 pt-6'>
								<button
									type='submit'
									disabled={isSubmitting}
									className='flex-1 bg-teal-500 text-white py-2 px-4 rounded-lg hover:bg-teal-600 focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2'
								>
									{isSubmitting ? (
										<>
											<FaSpinner className='animate-spin' />
											Saving...
										</>
									) : (
										<>
											<FaSave />
											Save Changes
										</>
									)}
								</button>
								
								<button
									type='button'
									onClick={handleCancel}
									disabled={isSubmitting}
									className='flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2'
								>
									<FaTimes />
									Cancel
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
			<Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
		</div>
	);
};

export default EditProfile;
